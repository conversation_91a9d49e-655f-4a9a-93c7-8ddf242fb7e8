Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.5f1 (923722cbbcfc) revision 9582370'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 195206 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-05-29T03:25:03Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.5f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game
-logFile
Logs/AssetImportWorker0.log
-srvPort
1186
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game
C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [30620]  Target information:

Player connection [30620]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1952896611 [EditorId] 1952896611 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-LI01V8I) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [30620]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1952896611 [EditorId] 1952896611 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-LI01V8I) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [30620]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1952896611 [EditorId] 1952896611 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-LI01V8I) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [30620] Host joined multi-casting on [***********:54997]...
Player connection [30620] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 8.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.5f1 (923722cbbcfc)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA RTX A5000 (ID=0x2231)
    Vendor:          NVIDIA
    VRAM:            24250 MB
    App VRAM Budget: 23482 MB
    Driver:          32.0.15.7159
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56360
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005158 seconds.
- Loaded All Assemblies, in  0.927 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.757 seconds
Domain Reload Profiling: 1681ms
	BeginReloadAssembly (266ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (79ms)
	RebuildNativeTypeToScriptingClass (62ms)
	initialDomainReloadingComplete (160ms)
	LoadAllAssembliesAndSetupDomain (357ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (352ms)
			TypeCache.Refresh (344ms)
				TypeCache.ScanAssembly (302ms)
			BuildScriptInfoCaches (6ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (757ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (647ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (135ms)
			SetLoadedEditorAssemblies (16ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (256ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.370 seconds
Refreshing native plugins compatible for Editor in 4.27 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.246 seconds
Domain Reload Profiling: 5590ms
	BeginReloadAssembly (543ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (135ms)
	RebuildCommonClasses (124ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (1551ms)
		LoadAssemblies (1270ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (587ms)
			TypeCache.Refresh (425ms)
				TypeCache.ScanAssembly (388ms)
			BuildScriptInfoCaches (138ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (3247ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2874ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (561ms)
			ProcessInitializeOnLoadAttributes (1812ms)
			ProcessInitializeOnLoadMethodAttributes (470ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (31ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 4.01 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6614 unused Assets / (5.6 MB). Loaded Objects now: 7291.
Memory consumption went from 166.3 MB to 160.7 MB.
Total: 25.088800 ms (FindLiveObjects: 1.799400 ms CreateObjectMapping: 1.930300 ms MarkObjects: 14.198900 ms  DeleteObjects: 7.158400 ms)

========================================================================
Received Import Request.
  Time since last request: 1085322.295667 seconds.
  path: Assets/InputSystem_Actions.inputactions
  artifactKey: Guid(2bcd2660ca9b64942af0de543d8d7100) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem_Actions.inputactions using Guid(2bcd2660ca9b64942af0de543d8d7100) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b247e7907b7d4a86176f4ae684b564d') in 0.0614548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/DefaultVolumeProfile.asset
  artifactKey: Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/DefaultVolumeProfile.asset using Guid(3f9215ea0144899419cfbc0957140d3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1e3ee85287093c2e01e2184a29fd3edf') in 0.0025361 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.602 seconds
Refreshing native plugins compatible for Editor in 3.05 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.199 seconds
Domain Reload Profiling: 3797ms
	BeginReloadAssembly (366ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (2097ms)
		LoadAssemblies (1520ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (738ms)
			TypeCache.Refresh (424ms)
				TypeCache.ScanAssembly (392ms)
			BuildScriptInfoCaches (289ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1200ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (972ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (276ms)
			ProcessInitializeOnLoadAttributes (549ms)
			ProcessInitializeOnLoadMethodAttributes (123ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.59 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 46 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7611 unused Assets / (7.4 MB). Loaded Objects now: 8270.
Memory consumption went from 160.9 MB to 153.5 MB.
Total: 19.427700 ms (FindLiveObjects: 1.246500 ms CreateObjectMapping: 1.566400 ms MarkObjects: 10.014200 ms  DeleteObjects: 6.598500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.618 seconds
Refreshing native plugins compatible for Editor in 2.36 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.239 seconds
Domain Reload Profiling: 2856ms
	BeginReloadAssembly (369ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (1130ms)
		LoadAssemblies (784ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (506ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (469ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1240ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (966ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (327ms)
			ProcessInitializeOnLoadAttributes (485ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 46 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7611 unused Assets / (7.2 MB). Loaded Objects now: 8273.
Memory consumption went from 162.8 MB to 155.6 MB.
Total: 16.860100 ms (FindLiveObjects: 1.284100 ms CreateObjectMapping: 1.822500 ms MarkObjects: 7.448700 ms  DeleteObjects: 6.303000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.594 seconds
Refreshing native plugins compatible for Editor in 9.95 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.208 seconds
Domain Reload Profiling: 2799ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (1133ms)
		LoadAssemblies (785ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (488ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (436ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1208ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (970ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (269ms)
			ProcessInitializeOnLoadAttributes (451ms)
			ProcessInitializeOnLoadMethodAttributes (233ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 46 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7745 unused Assets / (7.3 MB). Loaded Objects now: 8412.
Memory consumption went from 165.5 MB to 158.2 MB.
Total: 19.724100 ms (FindLiveObjects: 1.370800 ms CreateObjectMapping: 1.645900 ms MarkObjects: 10.643000 ms  DeleteObjects: 6.063100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.828 seconds
Refreshing native plugins compatible for Editor in 3.11 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.310 seconds
Domain Reload Profiling: 4133ms
	BeginReloadAssembly (349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (1329ms)
		LoadAssemblies (858ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (620ms)
			TypeCache.Refresh (55ms)
				TypeCache.ScanAssembly (32ms)
			BuildScriptInfoCaches (537ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (2311ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1799ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (199ms)
			BeforeProcessingInitializeOnLoad (492ms)
			ProcessInitializeOnLoadAttributes (666ms)
			ProcessInitializeOnLoadMethodAttributes (425ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 145 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8621 unused Assets / (24.3 MB). Loaded Objects now: 9191.
Memory consumption went from 207.8 MB to 183.5 MB.
Total: 34.583500 ms (FindLiveObjects: 2.122600 ms CreateObjectMapping: 2.477800 ms MarkObjects: 13.791600 ms  DeleteObjects: 16.189600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2888.502128 seconds.
  path: Assets/Muse Sprite.asset
  artifactKey: Guid(4399e92789cd2f444837de42e452846c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Muse Sprite.asset using Guid(4399e92789cd2f444837de42e452846c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce1c30ba4d795fbf81099113f54783c4') in 0.049278 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 19.457574 seconds.
  path: Assets/Muse Sprite.asset
  artifactKey: Guid(4399e92789cd2f444837de42e452846c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Muse Sprite.asset using Guid(4399e92789cd2f444837de42e452846c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a84a7d1357ba29b455f1b49cab37416') in 0.0079724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 259.132783 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '86e3c170f872c30f6de896e5a563bb91') in 0.0044406 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.692441 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b38c8e0c3197aafe8e11c2b55e0c11a') in 0.0063061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.309945 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '454c2586ff470d7bdd9dea96a5f44fc9') in 0.0025595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.845 seconds
Refreshing native plugins compatible for Editor in 3.41 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.455 seconds
Domain Reload Profiling: 3290ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (56ms)
	LoadAllAssembliesAndSetupDomain (1327ms)
		LoadAssemblies (901ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (574ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (517ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1456ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1225ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (361ms)
			ProcessInitializeOnLoadAttributes (563ms)
			ProcessInitializeOnLoadMethodAttributes (282ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 3.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8697 unused Assets / (22.4 MB). Loaded Objects now: 9255.
Memory consumption went from 213.5 MB to 191.1 MB.
Total: 26.305900 ms (FindLiveObjects: 1.432200 ms CreateObjectMapping: 1.937900 ms MarkObjects: 9.367400 ms  DeleteObjects: 13.566700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 7.35 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8685 unused Assets / (20.0 MB). Loaded Objects now: 9256.
Memory consumption went from 210.0 MB to 190.0 MB.
Total: 35.276800 ms (FindLiveObjects: 2.013100 ms CreateObjectMapping: 2.327100 ms MarkObjects: 16.339200 ms  DeleteObjects: 14.595500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.278 seconds
Refreshing native plugins compatible for Editor in 4.68 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.785 seconds
Domain Reload Profiling: 4063ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (23ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (1778ms)
		LoadAssemblies (1039ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (880ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (807ms)
			ResolveRequiredComponents (39ms)
	FinalizeReload (1785ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1346ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (445ms)
			ProcessInitializeOnLoadAttributes (552ms)
			ProcessInitializeOnLoadMethodAttributes (318ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 4.68 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8698 unused Assets / (24.6 MB). Loaded Objects now: 9260.
Memory consumption went from 211.5 MB to 186.9 MB.
Total: 27.826600 ms (FindLiveObjects: 1.458700 ms CreateObjectMapping: 1.932700 ms MarkObjects: 10.213100 ms  DeleteObjects: 14.220600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.664 seconds
Refreshing native plugins compatible for Editor in 2.66 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.380 seconds
Domain Reload Profiling: 3040ms
	BeginReloadAssembly (398ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (25ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (96ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (1154ms)
		LoadAssemblies (761ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (541ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (498ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1381ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1108ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (329ms)
			ProcessInitializeOnLoadAttributes (485ms)
			ProcessInitializeOnLoadMethodAttributes (270ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 2.63 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8698 unused Assets / (22.8 MB). Loaded Objects now: 9264.
Memory consumption went from 218.2 MB to 195.5 MB.
Total: 22.906300 ms (FindLiveObjects: 1.408700 ms CreateObjectMapping: 1.818600 ms MarkObjects: 7.767500 ms  DeleteObjects: 11.909900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4086.185964 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f270dae79ac936e2b1d1380f9b725e8') in 0.05241 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 40.030692 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72597ec1f5e6ee2d236f48abc62096d9') in 0.0033694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.344482 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b63c7cb47320d4fd42b7195ca20c696') in 0.0047397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.671414 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cebbe834c9361b7f14456d06c080a962') in 0.0033542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 40.751526 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '400159fd53d28a3976464507a512b442') in 0.005232 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 50.352744 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '560c5fb7976e6a8ce4722625cc635944') in 0.1572231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 11.193835 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '60b8c15e8534bfb37295d233224db314') in 0.0305729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 4.803171 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8bbe60f5b7099f8929fac0f910afcac8') in 0.0288161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 11.232102 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '72602b3766ee3872b70ada4d96e20b6b') in 0.0291451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 4.842197 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c6ce052a32fc09e7805290053a9b1f3e') in 0.0318926 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 3.567699 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '16a297bd9ee89483b6822350d8b7d9ab') in 0.0298596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 5.400746 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5dffc624e3eaa271e1f1d6ed47b1cd6b') in 0.0262436 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 10.496266 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af46c8ff23a92b131ae67501a4e18d21') in 0.028888 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 12.687611 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83e7f963670de46e9ab312f431809c09') in 0.0296613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 7.689437 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89b2ee7ff46f8fa70a0cbe63da2b2966') in 0.0331147 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 13.484743 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '936991937853d3a3921e73483a40b62b') in 0.0297805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 12.712356 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dfceb71a3eecd7e5e6e067c3d5c198e2') in 0.0316964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 340.262229 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '236a4563e555a3549dd717a11e6de415') in 0.0042222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.592375 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ac6b4cb38ccf671ee3dca7cd91693e86') in 0.0063724 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 52.927747 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b56dfa28c1ea2700051ca4e2c9b6ae5') in 0.004079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.238440 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fecf315a21508072f55f7a30de681229') in 0.0037183 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 25.945091 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd24ba597c0f345f24268de6443f538d9') in 0.0061316 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.080047 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46dfe9bc7d78fd49cf08a9463fd4e2e1') in 0.0061089 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.110803 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b313bd476ef486f16f1e1be35b3f81d') in 0.0053335 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 19.881594 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45dbefd0af155a02508824ae8cb73821') in 0.0059255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.877641 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bac37afb69e5c6ff1c8840ad37175c46') in 0.0058828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.579906 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a70ca81a236eb33047fd8ce0e828d0d') in 0.004341 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 16.722724 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fce8c15f5fd89b72abe1a6f39a67c8a0') in 0.0048217 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.094520 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b920475663f39a47fcd044bd964983fc') in 0.0054822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.156235 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b592a65996303ed5d0d10511f25e673') in 0.0048199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 17.233176 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83f12afb91ae0380ca2922fd075da307') in 0.0045255 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 9.760535 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '48043dea9a7d9aff17cb267bcb967bf1') in 0.0068274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.010232 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b16646526bde10c52e39b55ba6d1a85') in 0.0097612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.080872 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '770598aa4af8db885b3ee39c07b66ecc') in 0.0048389 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 29.124987 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8869f545ded079610b826420ed82bb13') in 0.0072628 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.048637 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '192c21d472e2260e46e99003bb4fe9bc') in 0.0051572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.124329 seconds.
  path: Assets/Blocks for mining.asset
  artifactKey: Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.asset using Guid(bb99f653541fb094cb6b920c79e63400) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e1bacb6d2d0595cec101b45d762425fb') in 0.0095466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 63.213939 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b69b58d86f786eb59075d8572484bcc') in 0.0351448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.726 seconds
Refreshing native plugins compatible for Editor in 3.55 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.356 seconds
Domain Reload Profiling: 3080ms
	BeginReloadAssembly (349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (21ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (86ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (1214ms)
		LoadAssemblies (822ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (528ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (485ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1357ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1064ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (303ms)
			ProcessInitializeOnLoadAttributes (494ms)
			ProcessInitializeOnLoadMethodAttributes (252ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.86 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 162 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8705 unused Assets / (25.8 MB). Loaded Objects now: 9279.
Memory consumption went from 213.6 MB to 187.8 MB.
Total: 26.327200 ms (FindLiveObjects: 1.454800 ms CreateObjectMapping: 1.918800 ms MarkObjects: 8.276600 ms  DeleteObjects: 14.675300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 285.833591 seconds.
  path: Assets/GameObject (1).prefab
  artifactKey: Guid(6b1fd76ed1d2d924a9612b39189c923d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/GameObject (1).prefab using Guid(6b1fd76ed1d2d924a9612b39189c923d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '51934d9127a44f14b3eb2d3876fed5bf') in 0.714958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 9.761602 seconds.
  path: Assets/Prefabs
  artifactKey: Guid(ce04eb9ae5344fb418892956fad11fd4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs using Guid(ce04eb9ae5344fb418892956fad11fd4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04cfd8b7e3b319aba02fb1a9019f941f') in 0.0007676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 10.888119 seconds.
  path: Assets/Prefabs/GameObject.prefab
  artifactKey: Guid(a53f52a41539c9f4fb5977d99ef2dada) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/GameObject.prefab using Guid(a53f52a41539c9f4fb5977d99ef2dada) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0503002c7aa78eaf560076aabbc4ca1d') in 0.025856 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 92.486797 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa1d5d9d7311b80ba4a397b5b86502e5') in 0.0020904 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 5.967937 seconds.
  path: Assets/New Material.mat
  artifactKey: Guid(908cdb382de8b424d88e4e6d58044bd5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New Material.mat using Guid(908cdb382de8b424d88e4e6d58044bd5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5d7dfc75cf40a833844852244ea73afb') in 0.0565199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 180.787322 seconds.
  path: Assets/Ruletile/Minetile.asset
  artifactKey: Guid(b52a6af64348ce74a94607fabc9bfeb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Ruletile/Minetile.asset using Guid(b52a6af64348ce74a94607fabc9bfeb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '52986fcee5d9ed675ac0cb345f04415e') in 0.00759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 461.933438 seconds.
  path: Assets/karsiori/Scenes/SampleScene - Woods Tileset and Background.unity
  artifactKey: Guid(c8eb94fb6ee6bf44eb7f1e239c5aa172) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/Scenes/SampleScene - Woods Tileset and Background.unity using Guid(c8eb94fb6ee6bf44eb7f1e239c5aa172) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af9b4753a3fb8116ddcc4174d48f8f5d') in 0.0010549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 16.661011 seconds.
  path: Assets/karsiori/TileMap/Backgrounds/BACKGROUND.png
  artifactKey: Guid(f44fd67bd73d546469d7c317fcd1d5ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds/BACKGROUND.png using Guid(f44fd67bd73d546469d7c317fcd1d5ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd9c4dbcd448a4daa280d64679def70bd') in 0.0553388 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/karsiori/TileMap/Backgrounds/WOODS - First.png
  artifactKey: Guid(4a0bf6774f40eeb419adfda6b54d5c1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds/WOODS - First.png using Guid(4a0bf6774f40eeb419adfda6b54d5c1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd448dd3e9d95ecfb5e90a60bd9aed79c') in 0.0308427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/karsiori/TileMap/Backgrounds/VINES - Second.png
  artifactKey: Guid(c3909c99bf845a24db9a162d677b6398) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds/VINES - Second.png using Guid(c3909c99bf845a24db9a162d677b6398) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce26043173296046fe2e8f106c025751') in 0.0241371 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000059 seconds.
  path: Assets/karsiori/TileMap/Backgrounds/WOODS - Third.png
  artifactKey: Guid(45367da5e1f745b4daacb977484a3704) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds/WOODS - Third.png using Guid(45367da5e1f745b4daacb977484a3704) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c15f9174634bcccb541463617f7bbcf9') in 0.0339014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.576851 seconds.
  path: Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-1.png
  artifactKey: Guid(390501f7c18ef384eb17584a5b462691) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-1.png using Guid(390501f7c18ef384eb17584a5b462691) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ddd54e255ad68d69da7d82ac27fb1b1a') in 0.0301236 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-5.png
  artifactKey: Guid(441f0c2aca2fff545bc517e6f139dcd3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-5.png using Guid(441f0c2aca2fff545bc517e6f139dcd3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bbff19c699e6130689b256fe262abd65') in 0.0341075 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/karsiori/TileMap/Decorations/GRASS 2-1.png
  artifactKey: Guid(978dc763e47067149be399903ad6addb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/GRASS 2-1.png using Guid(978dc763e47067149be399903ad6addb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25de5cc8f72108ac545b4a51d947df8d') in 0.0326894 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000078 seconds.
  path: Assets/karsiori/TileMap/Decorations/MUSHROOM 2-1.png
  artifactKey: Guid(bf1ec6d0f702cf64aa0dcef1dde074e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/MUSHROOM 2-1.png using Guid(bf1ec6d0f702cf64aa0dcef1dde074e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a03580e96681f7e3c900fd8b08197c73') in 0.0320862 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/karsiori/TileMap/Decorations/GRASS 1-1.png
  artifactKey: Guid(0601d1c0d4f47414d8f4753656fb214d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/GRASS 1-1.png using Guid(0601d1c0d4f47414d8f4753656fb214d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2da601cd2411b1f785c373307a8a80d2') in 0.0307081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/karsiori/TileMap/Decorations/MUSHROOM 1-1.png
  artifactKey: Guid(d45bba719c38eef499144f2e7a1d042f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/MUSHROOM 1-1.png using Guid(d45bba719c38eef499144f2e7a1d042f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '53baff16f5509c6c3119858c2ef7d3ee') in 0.0271004 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/karsiori/TileMap/Decorations/GRASS 3-1.png
  artifactKey: Guid(99da83ba741f67f499ae9ac3544776b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/GRASS 3-1.png using Guid(99da83ba741f67f499ae9ac3544776b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e67f8802cb4958f28c7b5a2df3c652c') in 0.0299418 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-6.png
  artifactKey: Guid(052ccebcbaae6204f803204b2645d91c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-6.png using Guid(052ccebcbaae6204f803204b2645d91c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4969649b43b360c96ec591101dab33c0') in 0.0260292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.339504 seconds.
  path: Assets/karsiori/TileMap/Material/New Physics Material 2D.physicsMaterial2D
  artifactKey: Guid(3b4e730b6b0a88f4f933a641f8b82695) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Material/New Physics Material 2D.physicsMaterial2D using Guid(3b4e730b6b0a88f4f933a641f8b82695) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c509dfdb15274e36461aa617e113d7f5') in 0.0019339 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 1.339557 seconds.
  path: Assets/karsiori/TileMap/Tiles/Rectangular Palette.prefab
  artifactKey: Guid(28bcf7a33d6ec764392ebb81ed9cf812) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Rectangular Palette.prefab using Guid(28bcf7a33d6ec764392ebb81ed9cf812) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8aa64f42780ec3d751f68d6bcb90b423') in 0.1065141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 187

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_0.asset
  artifactKey: Guid(ad573af9e5caa10478408d2df852f5f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_0.asset using Guid(ad573af9e5caa10478408d2df852f5f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f38da1e34e734fb3038625e9b0d0ec2d') in 0.0264441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_5.asset
  artifactKey: Guid(1cd5e881e4eb82544855bfe735525acf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_5.asset using Guid(1cd5e881e4eb82544855bfe735525acf) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1552450fa91a9c8dca5862ddf092213d') in 0.0245298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_40.asset
  artifactKey: Guid(bf913de136965a04493eddbf5c96054c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_40.asset using Guid(bf913de136965a04493eddbf5c96054c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a1cf9640425f28b23bef83bf1d4ffa2') in 0.0214574 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_27.asset
  artifactKey: Guid(de486fe804d2dc647845caefa2b7415c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_27.asset using Guid(de486fe804d2dc647845caefa2b7415c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9c81ff0d25506e419c6db675da4f295b') in 0.0227193 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_11.asset
  artifactKey: Guid(307b4843ef75d8e4b9f780ac1450fc82) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_11.asset using Guid(307b4843ef75d8e4b9f780ac1450fc82) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd147e3799c10d532c5c9da2ae598d93') in 0.0234117 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_23.asset
  artifactKey: Guid(a02a258e348d44447be48e199d885db9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_23.asset using Guid(a02a258e348d44447be48e199d885db9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7cf066fe90293ad78fc1c0e48eedade4') in 0.0215943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_18.asset
  artifactKey: Guid(077ce259dc0997241be9e537bf5662dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_18.asset using Guid(077ce259dc0997241be9e537bf5662dc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0ac473acf4ad18af86506e043b15deaa') in 0.0429755 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_15.asset
  artifactKey: Guid(7af7730f48b4b8643b73e4bb36b8d351) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_15.asset using Guid(7af7730f48b4b8643b73e4bb36b8d351) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18ce1de01a00369fe10e520b6135bbe3') in 0.021776 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_31.asset
  artifactKey: Guid(1837ddfc38e26874c8fbf33413a4fe28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_31.asset using Guid(1837ddfc38e26874c8fbf33413a4fe28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c1f016758e55c3b8cc882e365a276c60') in 0.0204637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_26.asset
  artifactKey: Guid(5b1b6f6e234b6f246be8ac8efcf00cbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_26.asset using Guid(5b1b6f6e234b6f246be8ac8efcf00cbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1f08cd4202b3885331492cf7cf4d19f2') in 0.0230175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_17.asset
  artifactKey: Guid(d0ba0aece1cfe864ca3317c8391ef9b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_17.asset using Guid(d0ba0aece1cfe864ca3317c8391ef9b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae9d4272f74ea6bb2661d89c098e8e60') in 0.0265323 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_32.asset
  artifactKey: Guid(715f043ffa3bd424a83d2f556a730f30) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_32.asset using Guid(715f043ffa3bd424a83d2f556a730f30) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50767522da6a5e45eeeced238e49783f') in 0.0245155 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_34.asset
  artifactKey: Guid(71fa0f766d9658f41bdc53c39b9d6f21) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_34.asset using Guid(71fa0f766d9658f41bdc53c39b9d6f21) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd92a1625c8b48ab5209be058a0bd6d62') in 0.0256986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_24.asset
  artifactKey: Guid(c6a266e3ea92b6d4696006347a1c16ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_24.asset using Guid(c6a266e3ea92b6d4696006347a1c16ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1b37de129c6d0b1166f65dd9a720a373') in 0.0208458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_3.asset
  artifactKey: Guid(0e842ce1003dd0e4faef6ac30bca5bf6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_3.asset using Guid(0e842ce1003dd0e4faef6ac30bca5bf6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c69b504a2fb5d9123118fe48fecb5cef') in 0.0191927 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_36.asset
  artifactKey: Guid(6db9757d4de818d4c82ea239822b40b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_36.asset using Guid(6db9757d4de818d4c82ea239822b40b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c7b83771c98b61e22317d836c850c8f4') in 0.0197634 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_19.asset
  artifactKey: Guid(f32d9e1260cb1dd4caa160ac17b4cafc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_19.asset using Guid(f32d9e1260cb1dd4caa160ac17b4cafc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '577774d7d830bfb6fdd34f0d1fe78f99') in 0.0220486 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_6.asset
  artifactKey: Guid(ff5d7bf85032bf64ea4ab58ad565ee45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_6.asset using Guid(ff5d7bf85032bf64ea4ab58ad565ee45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f93a871663933b9afa0c2f9575de933d') in 0.018611 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_16.asset
  artifactKey: Guid(425ac411c33352a489e445a32f1d7a98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_16.asset using Guid(425ac411c33352a489e445a32f1d7a98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3e72d378dbdaa8a43461f534fc7215f2') in 0.0203841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_22.asset
  artifactKey: Guid(918b83baafdb8df4fb83a2a397fdfb9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_22.asset using Guid(918b83baafdb8df4fb83a2a397fdfb9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9792719c10eec064a585fe0dc619c09') in 0.0206957 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_4.asset
  artifactKey: Guid(6e8de3d76be31834aac03dbad2304adb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_4.asset using Guid(6e8de3d76be31834aac03dbad2304adb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2148a5a5e35f9f1a5bb2b69768d8c42c') in 0.0204614 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_33.asset
  artifactKey: Guid(0aa13be0b5503e44994aa2ccd46e3e8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_33.asset using Guid(0aa13be0b5503e44994aa2ccd46e3e8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61334d6059b893798c3b3fb08f56f106') in 0.0224404 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_7.asset
  artifactKey: Guid(06a13e209e31d4e4783bef0cf85ff5e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_7.asset using Guid(06a13e209e31d4e4783bef0cf85ff5e9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '028d8c0456cf619f69e6e19c6f3b1ccb') in 0.018553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 2.142380 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_41.asset
  artifactKey: Guid(8775055d9fe32894fb2d26875a9620eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_41.asset using Guid(8775055d9fe32894fb2d26875a9620eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '865e76dd274c83843287843dcf61071f') in 0.0218272 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_49.asset
  artifactKey: Guid(d5cf73983213bdf4ebdf6308692316fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_49.asset using Guid(d5cf73983213bdf4ebdf6308692316fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '68c646ca0848d5fa2c9fbe29954a2042') in 0.0237076 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_65.asset
  artifactKey: Guid(9a2c4bd459586f145acf8cedccb4af24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_65.asset using Guid(9a2c4bd459586f145acf8cedccb4af24) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5e05e0094f3cf3edf50eeaa11735a94') in 0.0254806 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_61.asset
  artifactKey: Guid(4bd2271b3d6a67444937f0c92bd67018) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_61.asset using Guid(4bd2271b3d6a67444937f0c92bd67018) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '272c43dec89b928bf0b80f855ca2b666') in 0.0215408 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_44.asset
  artifactKey: Guid(3177598e71e6e6d4d884518d3f0eeb2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_44.asset using Guid(3177598e71e6e6d4d884518d3f0eeb2e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e74e1c54a97b944b61390bb135ca8bd9') in 0.0229158 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_56.asset
  artifactKey: Guid(4d4880ae419443a47ad9cdd5b2b0272f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_56.asset using Guid(4d4880ae419443a47ad9cdd5b2b0272f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c49cbe046a1fde7f297bc751dbe6e637') in 0.0248128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000295 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_62.asset
  artifactKey: Guid(faea2e6c5379be3489f734414daa59f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_62.asset using Guid(faea2e6c5379be3489f734414daa59f7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '298c6a7cbcea6f17c9526381b40ccf80') in 0.0363642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_53.asset
  artifactKey: Guid(6d04f7a0d537f8345a1518988b1255e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_53.asset using Guid(6d04f7a0d537f8345a1518988b1255e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b0bb312a2b0a53776f9cef3b9b6dcdf') in 0.0223537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_45.asset
  artifactKey: Guid(35b298bf341462e4095e32ad67cc37a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_45.asset using Guid(35b298bf341462e4095e32ad67cc37a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ffa55b7fc3d4007600f29cb3dc00f97') in 0.0227549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_60.asset
  artifactKey: Guid(71635bfc1cb53bd4c93add28d162422f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_60.asset using Guid(71635bfc1cb53bd4c93add28d162422f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a2b1504208e82b6977fb2ab605f1960') in 0.019441 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_66.asset
  artifactKey: Guid(e6b464b9871f4f545a44bd438bd57e12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_66.asset using Guid(e6b464b9871f4f545a44bd438bd57e12) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1ee92bf8d1cd681db786341b0cccfa6') in 0.0204097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_64.asset
  artifactKey: Guid(df52d654f80c6f843a559c24812871d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_64.asset using Guid(df52d654f80c6f843a559c24812871d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74041cee677c6200b41d6c91e7d9f157') in 0.0228176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_48.asset
  artifactKey: Guid(922baf4a166a73d4ab3600ffe6c863ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_48.asset using Guid(922baf4a166a73d4ab3600ffe6c863ac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8184d864ce38b44b1df89c647d0361bf') in 0.022866 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_51.asset
  artifactKey: Guid(296966da5c3138140967350445d97b05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_51.asset using Guid(296966da5c3138140967350445d97b05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45d774b9eed51e46eda137aa1fca8fb1') in 0.0256949 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 10.914119 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_69.asset
  artifactKey: Guid(9a817e38f14f6cc48a21bb505d35239c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_69.asset using Guid(9a817e38f14f6cc48a21bb505d35239c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21179f39249c031f69a66d4a2877716d') in 0.0156854 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_76.asset
  artifactKey: Guid(240e87253dd89084d91278b0e4798e70) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_76.asset using Guid(240e87253dd89084d91278b0e4798e70) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f9aad7cb26cc42c660fc356cff0a3c58') in 0.0218186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_79.asset
  artifactKey: Guid(5441669cb4f05af4d982b009bc284d13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_79.asset using Guid(5441669cb4f05af4d982b009bc284d13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a7310404f3a66e7b44d401a6032dc15') in 0.0200006 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_75.asset
  artifactKey: Guid(02a1e20da6d977a4dab913a8cee8a124) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_75.asset using Guid(02a1e20da6d977a4dab913a8cee8a124) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54d93e54a6dcf4e4fb9fb724068b4f17') in 0.0233523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_82.asset
  artifactKey: Guid(835b8a97f0f3e39488efffa1c3baeb14) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_82.asset using Guid(835b8a97f0f3e39488efffa1c3baeb14) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e554a88d983161eebbba99b30d5168de') in 0.0221293 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_78.asset
  artifactKey: Guid(d2c81206cf6c6604980b14a9d0e763cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_78.asset using Guid(d2c81206cf6c6604980b14a9d0e763cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '41be8e8a665872d59a941ae7d33a5a9f') in 0.0136081 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_84.asset
  artifactKey: Guid(bccdc2471a292c74f8d41f6fc8500c17) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_84.asset using Guid(bccdc2471a292c74f8d41f6fc8500c17) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '231a128296ca681fc0f0b454509b2080') in 0.0252799 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_73.asset
  artifactKey: Guid(3fd439784c4b99248b2f2beba089753c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_73.asset using Guid(3fd439784c4b99248b2f2beba089753c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e8ba8761ec7a83bb26066a2c108d2038') in 0.0271225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_77.asset
  artifactKey: Guid(a9f503f4786af684da1de1c801b84e65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_77.asset using Guid(a9f503f4786af684da1de1c801b84e65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5982bf6acf075a7caaecf438aba5e9ae') in 0.0228622 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 56.135366 seconds.
  path: Assets/karsiori/TileMap/Backgrounds
  artifactKey: Guid(d74bfa70216838f48920b22d92b9b272) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds using Guid(d74bfa70216838f48920b22d92b9b272) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1051fda4c0268ae5482cf3e102339d6') in 0.00084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.371198 seconds.
  path: Assets/karsiori/TileMap/Decorations
  artifactKey: Guid(8c8aacf5e651f5c4f925b03ec1f407d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations using Guid(8c8aacf5e651f5c4f925b03ec1f407d8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd396a821f6c9ae3d62a25632f500972') in 0.0007501 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.373751 seconds.
  path: Assets/karsiori/TileMap/Material
  artifactKey: Guid(a7b2b8e7d0ef390448020c207c7d666c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Material using Guid(a7b2b8e7d0ef390448020c207c7d666c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '586b2a7fd460e8ec6869a69bb5d4d77c') in 0.0009157 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.402394 seconds.
  path: Assets/karsiori/TileMap/Tiles
  artifactKey: Guid(08c205ff6de376945bb30e4a3620c576) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles using Guid(08c205ff6de376945bb30e4a3620c576) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e474fabb1444102eb53666d04fbb3ac4') in 0.0007149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 251.432131 seconds.
  path: Assets/karsiori/TileMap/Tiles/Rectangular Palette.prefab
  artifactKey: Guid(28bcf7a33d6ec764392ebb81ed9cf812) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Rectangular Palette.prefab using Guid(28bcf7a33d6ec764392ebb81ed9cf812) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a85cee74dc182bc6bb279c23f9e5b5a9') in 0.057891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 187

========================================================================
Received Import Request.
  Time since last request: 534.489539 seconds.
  path: Assets/Warrior free set/Sprite Sheet/Warrior_Sheet-Effect.png
  artifactKey: Guid(ae72133dacba6ec4eb33ecde48f50e56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Sprite Sheet/Warrior_Sheet-Effect.png using Guid(ae72133dacba6ec4eb33ecde48f50e56) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0cf5cd392412d5c995b10256ba9a3b8e') in 0.4103822 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 101

========================================================================
Received Import Request.
  Time since last request: 6.940501 seconds.
  path: Assets/Warrior free set/Demo.unity
  artifactKey: Guid(14a2430fd109a08429c462b16fc369a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Demo.unity using Guid(14a2430fd109a08429c462b16fc369a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3b7d742dfe8d02b78ed61a8b1adfd9b1') in 0.0008416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 13.138181 seconds.
  path: Assets/Warrior free set/Sprite Sheet
  artifactKey: Guid(8ec5f93311c2dfe44b0c9b3a026749c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Sprite Sheet using Guid(8ec5f93311c2dfe44b0c9b3a026749c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '163e299192cc94e61b8b0f96e210dd5d') in 0.0009956 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 2.622396 seconds.
  path: Assets/Warrior free set/Aniamtion
  artifactKey: Guid(f29f322a060ffae4fae45df37a157ed2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Aniamtion using Guid(f29f322a060ffae4fae45df37a157ed2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3fa01701b92332d0ddf1abcc10218d0b') in 0.0007265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 3.340505 seconds.
  path: Assets/Warrior free set/Aniamtion/Warrior.controller
  artifactKey: Guid(312496b25d842af4a938e96c7d5aaee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Aniamtion/Warrior.controller using Guid(312496b25d842af4a938e96c7d5aaee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '395ca4526a8d4c548b6ba9a9c4815ae0') in 0.0101787 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 44

========================================================================
Received Import Request.
  Time since last request: 34.325679 seconds.
  path: Assets/Ruletile/Minetile.asset
  artifactKey: Guid(b52a6af64348ce74a94607fabc9bfeb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Ruletile/Minetile.asset using Guid(b52a6af64348ce74a94607fabc9bfeb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '70540026796f4f90b5350f47df44b77f') in 0.0058132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.837578 seconds.
  path: Assets/Scenes/SampleScene.unity
  artifactKey: Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/SampleScene.unity using Guid(8c9cfa26abfee488c85f1582747f6a02) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c54a3ab8b28be491f05a1fd7cbc09a4b') in 0.0010696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 127.046146 seconds.
  path: Assets/Warrior free set/Sprite Sheet/Warrior_Sheet-Effect.png
  artifactKey: Guid(ae72133dacba6ec4eb33ecde48f50e56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Sprite Sheet/Warrior_Sheet-Effect.png using Guid(ae72133dacba6ec4eb33ecde48f50e56) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54319d4022321d1888a3647754214c0c') in 0.4822139 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 101

========================================================================
Received Import Request.
  Time since last request: 31.775514 seconds.
  path: Assets/Warrior free set/Aniamtion/WallSlide NoDust.anim
  artifactKey: Guid(beb4e910fbfd19b4eb5736606b0d4d2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Aniamtion/WallSlide NoDust.anim using Guid(beb4e910fbfd19b4eb5736606b0d4d2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ecee0ca1a7c330be2979935190cfd658') in 0.0022313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.504271 seconds.
  path: Assets/Warrior free set/Aniamtion/Wall-Slide.anim
  artifactKey: Guid(cc1b4dcb9b708d14293484dec9d970a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Aniamtion/Wall-Slide.anim using Guid(cc1b4dcb9b708d14293484dec9d970a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d182e9d625a6bae2a6b80944171752f') in 0.0015651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.778037 seconds.
  path: Assets/Warrior free set/Aniamtion/Slide.anim
  artifactKey: Guid(bd6167e0337245449a259e293568b4bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Aniamtion/Slide.anim using Guid(bd6167e0337245449a259e293568b4bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0cc1e0b882ac3919927d598ebf29425f') in 0.0016587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.554790 seconds.
  path: Assets/Warrior free set/Aniamtion/Run.anim
  artifactKey: Guid(aa852921ab2da0f4aaee7788209e4ca4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Aniamtion/Run.anim using Guid(aa852921ab2da0f4aaee7788209e4ca4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fb9af7bda02fe419810b8e39b787dade') in 0.0020944 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

