Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.5f1 (923722cbbcfc) revision 9582370'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 195206 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-05-29T03:25:03Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.5f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game
-logFile
Logs/AssetImportWorker1.log
-srvPort
1186
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game
C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [31384]  Target information:

Player connection [31384]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 4189187814 [EditorId] 4189187814 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-LI01V8I) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31384]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 4189187814 [EditorId] 4189187814 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-LI01V8I) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31384]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 4189187814 [EditorId] 4189187814 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-LI01V8I) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31384] Host joined multi-casting on [***********:54997]...
Player connection [31384] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 8.69 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.5f1 (923722cbbcfc)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Desktop/MGB WebApp Interactive Game/Interactive Web App Game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA RTX A5000 (ID=0x2231)
    Vendor:          NVIDIA
    VRAM:            24250 MB
    App VRAM Budget: 23482 MB
    Driver:          32.0.15.7159
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56128
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.5f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004506 seconds.
- Loaded All Assemblies, in  0.884 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.764 seconds
Domain Reload Profiling: 1615ms
	BeginReloadAssembly (265ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (83ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (121ms)
	LoadAllAssembliesAndSetupDomain (356ms)
		LoadAssemblies (262ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (348ms)
			TypeCache.Refresh (347ms)
				TypeCache.ScanAssembly (319ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (765ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (154ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (136ms)
			ProcessInitializeOnLoadAttributes (249ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  2.419 seconds
Refreshing native plugins compatible for Editor in 2.85 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.250 seconds
Domain Reload Profiling: 5657ms
	BeginReloadAssembly (522ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (138ms)
	RebuildNativeTypeToScriptingClass (51ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (1603ms)
		LoadAssemblies (1334ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (589ms)
			TypeCache.Refresh (424ms)
				TypeCache.ScanAssembly (392ms)
			BuildScriptInfoCaches (136ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (3254ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2937ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (25ms)
			BeforeProcessingInitializeOnLoad (523ms)
			ProcessInitializeOnLoadAttributes (1919ms)
			ProcessInitializeOnLoadMethodAttributes (442ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 3.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6614 unused Assets / (5.7 MB). Loaded Objects now: 7291.
Memory consumption went from 166.0 MB to 160.4 MB.
Total: 23.038900 ms (FindLiveObjects: 2.120400 ms CreateObjectMapping: 2.112600 ms MarkObjects: 12.017300 ms  DeleteObjects: 6.786500 ms)

========================================================================
Received Import Request.
  Time since last request: 1085322.312714 seconds.
  path: Assets/UniversalRenderPipelineGlobalSettings.asset
  artifactKey: Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UniversalRenderPipelineGlobalSettings.asset using Guid(93b439a37f63240aca3dd4e01d978a9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2b369817345c8c575b444408afdb8962') in 0.0533953 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.580 seconds
Refreshing native plugins compatible for Editor in 2.97 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.195 seconds
Domain Reload Profiling: 3771ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (2097ms)
		LoadAssemblies (1500ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (743ms)
			TypeCache.Refresh (430ms)
				TypeCache.ScanAssembly (399ms)
			BuildScriptInfoCaches (288ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1195ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (969ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (277ms)
			ProcessInitializeOnLoadAttributes (535ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 3.48 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 46 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7611 unused Assets / (7.2 MB). Loaded Objects now: 8270.
Memory consumption went from 160.6 MB to 153.4 MB.
Total: 24.130500 ms (FindLiveObjects: 1.633600 ms CreateObjectMapping: 1.880400 ms MarkObjects: 12.729500 ms  DeleteObjects: 7.885500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.618 seconds
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.222 seconds
Domain Reload Profiling: 2836ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (1128ms)
		LoadAssemblies (774ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (503ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (468ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1223ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (965ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (327ms)
			ProcessInitializeOnLoadAttributes (490ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.23 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 46 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7611 unused Assets / (7.2 MB). Loaded Objects now: 8273.
Memory consumption went from 162.5 MB to 155.3 MB.
Total: 17.725800 ms (FindLiveObjects: 1.271200 ms CreateObjectMapping: 2.060200 ms MarkObjects: 7.752200 ms  DeleteObjects: 6.641100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.600 seconds
Refreshing native plugins compatible for Editor in 4.23 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.180 seconds
Domain Reload Profiling: 2765ms
	BeginReloadAssembly (349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (1086ms)
		LoadAssemblies (762ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (482ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (437ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1180ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (968ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (266ms)
			ProcessInitializeOnLoadAttributes (450ms)
			ProcessInitializeOnLoadMethodAttributes (234ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.57 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 46 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7745 unused Assets / (8.5 MB). Loaded Objects now: 8412.
Memory consumption went from 165.3 MB to 156.8 MB.
Total: 23.954000 ms (FindLiveObjects: 1.295600 ms CreateObjectMapping: 1.623300 ms MarkObjects: 13.036300 ms  DeleteObjects: 7.997500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.853 seconds
Refreshing native plugins compatible for Editor in 6.63 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.211 seconds
Domain Reload Profiling: 4060ms
	BeginReloadAssembly (366ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (32ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (94ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (1329ms)
		LoadAssemblies (848ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (617ms)
			TypeCache.Refresh (45ms)
				TypeCache.ScanAssembly (24ms)
			BuildScriptInfoCaches (540ms)
			ResolveRequiredComponents (22ms)
	FinalizeReload (2213ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1724ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (26ms)
			SetLoadedEditorAssemblies (122ms)
			BeforeProcessingInitializeOnLoad (505ms)
			ProcessInitializeOnLoadAttributes (664ms)
			ProcessInitializeOnLoadMethodAttributes (395ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 145 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8621 unused Assets / (23.0 MB). Loaded Objects now: 9191.
Memory consumption went from 207.5 MB to 184.5 MB.
Total: 32.820600 ms (FindLiveObjects: 2.241000 ms CreateObjectMapping: 2.456600 ms MarkObjects: 11.245000 ms  DeleteObjects: 16.875400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.858 seconds
Refreshing native plugins compatible for Editor in 2.84 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.446 seconds
Domain Reload Profiling: 3299ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (1365ms)
		LoadAssemblies (915ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (599ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (543ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1447ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1203ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (367ms)
			ProcessInitializeOnLoadAttributes (519ms)
			ProcessInitializeOnLoadMethodAttributes (295ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.19 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8697 unused Assets / (25.2 MB). Loaded Objects now: 9255.
Memory consumption went from 214.3 MB to 189.1 MB.
Total: 32.214100 ms (FindLiveObjects: 1.440400 ms CreateObjectMapping: 1.879100 ms MarkObjects: 12.870600 ms  DeleteObjects: 16.021100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.42 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8685 unused Assets / (23.1 MB). Loaded Objects now: 9256.
Memory consumption went from 210.7 MB to 187.7 MB.
Total: 40.668900 ms (FindLiveObjects: 2.820100 ms CreateObjectMapping: 2.745400 ms MarkObjects: 15.525800 ms  DeleteObjects: 19.574100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.184 seconds
Refreshing native plugins compatible for Editor in 3.38 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.768 seconds
Domain Reload Profiling: 3951ms
	BeginReloadAssembly (353ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (108ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (1679ms)
		LoadAssemblies (1010ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (802ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (746ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1769ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1407ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (507ms)
			ProcessInitializeOnLoadAttributes (570ms)
			ProcessInitializeOnLoadMethodAttributes (308ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 3.73 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8698 unused Assets / (24.5 MB). Loaded Objects now: 9260.
Memory consumption went from 217.4 MB to 192.9 MB.
Total: 31.923300 ms (FindLiveObjects: 1.964200 ms CreateObjectMapping: 2.791900 ms MarkObjects: 12.317300 ms  DeleteObjects: 14.848300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.639 seconds
Refreshing native plugins compatible for Editor in 2.86 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.324 seconds
Domain Reload Profiling: 2960ms
	BeginReloadAssembly (387ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (1137ms)
		LoadAssemblies (751ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (527ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (485ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1325ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1081ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (337ms)
			ProcessInitializeOnLoadAttributes (474ms)
			ProcessInitializeOnLoadMethodAttributes (254ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.54 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 161 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8698 unused Assets / (24.6 MB). Loaded Objects now: 9264.
Memory consumption went from 224.1 MB to 199.5 MB.
Total: 24.335500 ms (FindLiveObjects: 1.457000 ms CreateObjectMapping: 1.791600 ms MarkObjects: 8.303800 ms  DeleteObjects: 12.781600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7257.802552 seconds.
  path: Assets/Blocks for mining.png
  artifactKey: Guid(02f377a35b5a2b240a1e7b55ac0f76f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Blocks for mining.png using Guid(02f377a35b5a2b240a1e7b55ac0f76f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '755689cc42cc6b6dccfa059b4ad24c72') in 0.1777954 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 83.857607 seconds.
  path: Assets/flat block.png
  artifactKey: Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/flat block.png using Guid(2534e57ceb8d05e47b2029fb5673b7c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ff693cc649de6b9f739343464eb9524') in 0.0368841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.708 seconds
Refreshing native plugins compatible for Editor in 3.70 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.371 seconds
Domain Reload Profiling: 3072ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (82ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (1201ms)
		LoadAssemblies (817ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (535ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (495ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1372ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1110ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (341ms)
			ProcessInitializeOnLoadAttributes (484ms)
			ProcessInitializeOnLoadMethodAttributes (263ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.58 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 162 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8705 unused Assets / (24.8 MB). Loaded Objects now: 9279.
Memory consumption went from 213.4 MB to 188.6 MB.
Total: 25.756300 ms (FindLiveObjects: 1.467800 ms CreateObjectMapping: 1.920800 ms MarkObjects: 8.570800 ms  DeleteObjects: 13.795400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1808.616862 seconds.
  path: Assets/karsiori/TileMap/Backgrounds/BUSH - BACKGROUND.png
  artifactKey: Guid(6e0fcddb85b3bfd4a9a26b3143f5d12a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds/BUSH - BACKGROUND.png using Guid(6e0fcddb85b3bfd4a9a26b3143f5d12a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc19c40348be7d68ecd886e1db9392f4') in 0.1279607 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/karsiori/TileMap/Backgrounds/WOODS - Second.png
  artifactKey: Guid(066d770ac5ce1af4ca576794cfe6704e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds/WOODS - Second.png using Guid(066d770ac5ce1af4ca576794cfe6704e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ebc2f49c2e9054b61545146c70b9167d') in 0.0268812 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/karsiori/TileMap/Backgrounds/WOODS - Fourth.png
  artifactKey: Guid(0e8e998796118084d8b33b5085a3560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Backgrounds/WOODS - Fourth.png using Guid(0e8e998796118084d8b33b5085a3560b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '538e058d03ede9627f92b4883658b972') in 0.0308589 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.579301 seconds.
  path: Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-2.png
  artifactKey: Guid(172cec936be54c1419615eb2b96b32a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-2.png using Guid(172cec936be54c1419615eb2b96b32a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5063d20e54f08fb2f70a580b1a09b75d') in 0.0300199 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-4.png
  artifactKey: Guid(204aa51c2bf1ec24581c9d3474d90682) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-4.png using Guid(204aa51c2bf1ec24581c9d3474d90682) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '57f03586b51464af1ad3e0d494b04bc0') in 0.0309732 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-3.png
  artifactKey: Guid(a97b342cc5260f04cafe1eb0535e607f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-3.png using Guid(a97b342cc5260f04cafe1eb0535e607f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83e9afdabb3c628fbd81187c9e1e86b5') in 0.0327526 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000107 seconds.
  path: Assets/karsiori/TileMap/Decorations/GRASS 2-2.png
  artifactKey: Guid(1ee73367054f56241a4324eb7326adfa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/GRASS 2-2.png using Guid(1ee73367054f56241a4324eb7326adfa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4b259d6b25538cf6ead6f6045ff75a9a') in 0.0330017 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/karsiori/TileMap/Decorations/GRASS 3-2.png
  artifactKey: Guid(86647a41d202c7d42bb73668e9f329b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/GRASS 3-2.png using Guid(86647a41d202c7d42bb73668e9f329b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd7382c38d0de5c7dc9b76f80ba8fecd2') in 0.0329929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/karsiori/TileMap/Decorations/GRASS 1-2.png
  artifactKey: Guid(968fb733146be924a9feb390a617f3e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/GRASS 1-2.png using Guid(968fb733146be924a9feb390a617f3e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa7a0f7d89a0c1df2c08f81a251aef57') in 0.022463 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/karsiori/TileMap/Decorations/MUSHROOM 1-2.png
  artifactKey: Guid(86a1d6e5913974b46810fb24e91348a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/MUSHROOM 1-2.png using Guid(86a1d6e5913974b46810fb24e91348a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '97dc88eb1208517ddd23a345be180692') in 0.0309443 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-7.png
  artifactKey: Guid(68304e1b7849e7843b7784e79792ff89) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Decorations/BUSH FOREGROUND 1-7.png using Guid(68304e1b7849e7843b7784e79792ff89) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a17fc5de6310aecf6813932902ab1db1') in 0.0260938 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 1.744490 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS.png
  artifactKey: Guid(b425613db706e0a49821a35253ec9db1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS.png using Guid(b425613db706e0a49821a35253ec9db1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '245d2800a175bc9013552b3dd0cbe47a') in 0.3625663 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 90

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_28.asset
  artifactKey: Guid(c30676a20468f1343b7f6687ccafa51b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_28.asset using Guid(c30676a20468f1343b7f6687ccafa51b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74a9e152f6c278178c575a2b350090e8') in 0.0737706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_2.asset
  artifactKey: Guid(e19814bf6ff84a34e855cac502c99f01) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_2.asset using Guid(e19814bf6ff84a34e855cac502c99f01) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '23a5abe47c8b7f6e403b9651836ed167') in 0.0211619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_13.asset
  artifactKey: Guid(0093f13f48b3cd24dad4ba4d67cd8409) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_13.asset using Guid(0093f13f48b3cd24dad4ba4d67cd8409) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7f575da3ff6e2d5ec99f3e1940a5fa70') in 0.0445481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_30.asset
  artifactKey: Guid(bfdfe9e169b2ab046845254e1bc615c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_30.asset using Guid(bfdfe9e169b2ab046845254e1bc615c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7d56c5cdbac94db1085f8a557875fb3a') in 0.0192736 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_10.asset
  artifactKey: Guid(8957af186e093f647bb7ff79bfc23fdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_10.asset using Guid(8957af186e093f647bb7ff79bfc23fdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd4ecebed305644b9852889a934d0dd0f') in 0.0222148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_14.asset
  artifactKey: Guid(2f1cd3866e345c24e907ed9b3e88cd2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_14.asset using Guid(2f1cd3866e345c24e907ed9b3e88cd2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e585da3bde629958ead698a88328b90e') in 0.0222189 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_20.asset
  artifactKey: Guid(a67930f876cbfed41b64d76170a83c9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_20.asset using Guid(a67930f876cbfed41b64d76170a83c9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d384a893f32a7efb450af6582a607ae') in 0.0320234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_9.asset
  artifactKey: Guid(b467cdb774d96404ab0bf719c7daff20) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_9.asset using Guid(b467cdb774d96404ab0bf719c7daff20) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'feac4f48714c64910b6c0b3987774b4a') in 0.0281964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_21.asset
  artifactKey: Guid(f9c7ab8d3aab8cb448dd7810f6cabdb4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_21.asset using Guid(f9c7ab8d3aab8cb448dd7810f6cabdb4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1521324be63fec2b12b9267c2e020ebe') in 0.025691 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_29.asset
  artifactKey: Guid(53acc36a9e4920b41975dd31e092b62a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_29.asset using Guid(53acc36a9e4920b41975dd31e092b62a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '76d44a6c8e77ad27294e772dd50c763a') in 0.022937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_1.asset
  artifactKey: Guid(b70c4d5c642b80d4a9c90c3aa87b520d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_1.asset using Guid(b70c4d5c642b80d4a9c90c3aa87b520d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db11916814e90ce95134cc71aecb6ca2') in 0.0214637 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_39.asset
  artifactKey: Guid(81eca0ad7f8af514cbd33438ee4b4a46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_39.asset using Guid(81eca0ad7f8af514cbd33438ee4b4a46) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b6e145796087b691ba10d329c157f6a4') in 0.0220212 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_38.asset
  artifactKey: Guid(252ca5911690afc41a21c9886b175631) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_38.asset using Guid(252ca5911690afc41a21c9886b175631) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e4f4ecebc2d51f7c724154bb998d4f2e') in 0.0201676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_12.asset
  artifactKey: Guid(8d6f378663f1a7448962dcdd727a4944) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_12.asset using Guid(8d6f378663f1a7448962dcdd727a4944) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '27eb8dabc9b42e1bbbdbbabc6ba92ce5') in 0.0197124 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_37.asset
  artifactKey: Guid(75abc7e4d64066d43817c8e608472486) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_37.asset using Guid(75abc7e4d64066d43817c8e608472486) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '43d70565c7d60a853dc69dc6085225ac') in 0.0211694 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_25.asset
  artifactKey: Guid(6359062e2e25b934db5a935d4bcf1ff9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_25.asset using Guid(6359062e2e25b934db5a935d4bcf1ff9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9270cd96706a88e06b5de132a2e8aa10') in 0.0235739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_8.asset
  artifactKey: Guid(094dd37345348064a8558780b21d4f98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_8.asset using Guid(094dd37345348064a8558780b21d4f98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '36848baf94982f3bbd7fb650fa2d5a18') in 0.0214883 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_35.asset
  artifactKey: Guid(82a0ff2832f81a6419401c36942e6ab4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_35.asset using Guid(82a0ff2832f81a6419401c36942e6ab4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ee301db3a340ac3700fff38438dcccc4') in 0.0226064 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 2.210236 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_42.asset
  artifactKey: Guid(9418a91d3bc5ad3449c31778c048790f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_42.asset using Guid(9418a91d3bc5ad3449c31778c048790f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '88bf51390e0c0d4311471886c8fca18e') in 0.0248906 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_47.asset
  artifactKey: Guid(0ea48912424104240be41a1239c51dcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_47.asset using Guid(0ea48912424104240be41a1239c51dcb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2a95e63f52e731680f3627433a47d366') in 0.0253497 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_50.asset
  artifactKey: Guid(62436fafb1e405d46bb04f84d70c2d62) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_50.asset using Guid(62436fafb1e405d46bb04f84d70c2d62) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5e800d5a1fbf506827778169a9619344') in 0.0240881 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_63.asset
  artifactKey: Guid(60ca663bf4893084c92a767875f28c26) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_63.asset using Guid(60ca663bf4893084c92a767875f28c26) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c5ed0f114279f88078482f8cef197105') in 0.021308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_54.asset
  artifactKey: Guid(4e2aa78de94672940906049e0b6677b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_54.asset using Guid(4e2aa78de94672940906049e0b6677b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a37284bf7acafcea36a84805482805cf') in 0.0227472 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_55.asset
  artifactKey: Guid(575c86391df21464495585478ed89557) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_55.asset using Guid(575c86391df21464495585478ed89557) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '83fd0ba3abf1ecabfb0e9dbf1229989e') in 0.0286936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_68.asset
  artifactKey: Guid(8d16683b6547c684781df31363626cb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_68.asset using Guid(8d16683b6547c684781df31363626cb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ffcbbb1a0fe806b3058d3a546dd77d4a') in 0.0349283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_52.asset
  artifactKey: Guid(e0776e8f174f6b640b7fa4e9bf1b9187) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_52.asset using Guid(e0776e8f174f6b640b7fa4e9bf1b9187) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a659001911fb7dce2841b448431e4819') in 0.0229027 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_43.asset
  artifactKey: Guid(85fa7f3358060554e8cded7c88e2d920) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_43.asset using Guid(85fa7f3358060554e8cded7c88e2d920) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd36e4a8031b9fd974e6e857bda25c457') in 0.0217903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_59.asset
  artifactKey: Guid(18b9aafa1f6d69f4fba252f4f7537fc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_59.asset using Guid(18b9aafa1f6d69f4fba252f4f7537fc1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab3af6cfc4df85be5a662aeb589096d4') in 0.0222705 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_46.asset
  artifactKey: Guid(ed23f2ec3411a8b4082d0c0ce32e7b81) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_46.asset using Guid(ed23f2ec3411a8b4082d0c0ce32e7b81) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54d9f04246a6159af63e947a012194ac') in 0.0216676 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_67.asset
  artifactKey: Guid(4c5fe05048939be41b6d4df2062808b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_67.asset using Guid(4c5fe05048939be41b6d4df2062808b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a29c882a7c1657904d8e115604ac4c95') in 0.0210615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_58.asset
  artifactKey: Guid(04d699b0e5568554f9e270084254616e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_58.asset using Guid(04d699b0e5568554f9e270084254616e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dee2b6007598e37b39d6c793cc484291') in 0.0228543 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_57.asset
  artifactKey: Guid(3a2a73ee7d3ba0c4ebd81cde7c19fb83) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_57.asset using Guid(3a2a73ee7d3ba0c4ebd81cde7c19fb83) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ce73d916eff44c470307b959ea94a0f5') in 0.0209636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 10.916219 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_70.asset
  artifactKey: Guid(0a09e1992de8ed64f916979302ca1b3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_70.asset using Guid(0a09e1992de8ed64f916979302ca1b3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8bf8d0c6d0da11ee1bddd600977104e') in 0.0310283 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_86.asset
  artifactKey: Guid(86aa9696dab2d674e95b2cb55e369b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_86.asset using Guid(86aa9696dab2d674e95b2cb55e369b40) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a2a69e5bb1e7c346a0ea6d7739fe7134') in 0.0113522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_74.asset
  artifactKey: Guid(3061cf2f556e3f84989a17004da8c87c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_74.asset using Guid(3061cf2f556e3f84989a17004da8c87c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2510f9ab174ed9a1b1a6fe0d2e738503') in 0.0199166 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_81.asset
  artifactKey: Guid(2adf5a8631b4c4d40a272ad9d2a4cb00) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_81.asset using Guid(2adf5a8631b4c4d40a272ad9d2a4cb00) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd096629177b6e01641b7ed9b564c76be') in 0.0219433 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_83.asset
  artifactKey: Guid(588379265f4151348aae5b925970dc7f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_83.asset using Guid(588379265f4151348aae5b925970dc7f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21765427f3eacd4993629df81882a445') in 0.0244333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_71.asset
  artifactKey: Guid(36cbf8b8f9cc9014fb00c7ee7b59d85b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_71.asset using Guid(36cbf8b8f9cc9014fb00c7ee7b59d85b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4deee0202938b8d03dd6344bab64f92e') in 0.0258973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_72.asset
  artifactKey: Guid(35a002b44883d0942929e424d3c5bb5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_72.asset using Guid(35a002b44883d0942929e424d3c5bb5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7bff039a078c5af6d3b8875b0ce2f2ee') in 0.0250246 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_85.asset
  artifactKey: Guid(0d9ea76dc1b9df846b2dcb5ab1a61994) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_85.asset using Guid(0d9ea76dc1b9df846b2dcb5ab1a61994) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '224bfd71e1637c442dead76815e592f4') in 0.0141088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_87.asset
  artifactKey: Guid(31ce9cdfaa5d91b40bf4d34d4969f6fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_87.asset using Guid(31ce9cdfaa5d91b40bf4d34d4969f6fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa1836a9ad35085f47d9f51cd6df1abf') in 0.0114537 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_80.asset
  artifactKey: Guid(8def8cfde5111094ea46b150be16f999) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/karsiori/TileMap/Tiles/Tilesheet - WOODS_80.asset using Guid(8def8cfde5111094ea46b150be16f999) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '060b59a932ef8243080e3ca786058728') in 0.0100522 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 843.505824 seconds.
  path: Assets/Warrior free set/Sprite Sheet/Warrior_SheetnoEffect.png
  artifactKey: Guid(3accdbde4e56f5c4e9cfa311b8cef55f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Warrior free set/Sprite Sheet/Warrior_SheetnoEffect.png using Guid(3accdbde4e56f5c4e9cfa311b8cef55f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04036dfee8953d8d85c11e714cf2021d') in 0.4169413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 101

