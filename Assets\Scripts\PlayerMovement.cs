using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerMovement : MonoBehaviour
{
    [Header("Movement Settings")]
    public float moveSpeed = 5f;
    public float jumpForce = 10f;
    public float dashForce = 15f;
    public float dashDuration = 0.2f;
    public float slideForce = 8f;
    public float slideDuration = 0.5f;

    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundCheckRadius = 0.2f;
    public LayerMask groundLayerMask;

    private Rigidbody2D rb;
    private Animator animator;
    private SpriteRenderer spriteRenderer;
    private bool isGrounded;
    private Vector2 moveInput;
    private bool jumpPressed;
    private bool isDashing = false;
    private bool isSliding = false;
    private float dashTimer = 0f;
    private float slideTimer = 0f;
    private bool facingRight = true;

    void Start()
    {
        // Get the Rigidbody2D component
        rb = GetComponent<Rigidbody2D>();

        // Get the Animator component
        animator = GetComponent<Animator>();

        // Get the SpriteRenderer component
        spriteRenderer = GetComponent<SpriteRenderer>();

        // If no ground check transform is assigned, create one
        if (groundCheck == null)
        {
            GameObject groundCheckObj = new GameObject("GroundCheck");
            groundCheckObj.transform.SetParent(transform);
            groundCheckObj.transform.localPosition = new Vector3(0, -0.5f, 0);
            groundCheck = groundCheckObj.transform;
        }
    }

    void Update()
    {
        // Check if player is grounded
        CheckGrounded();

        // Handle timers for special moves
        HandleSpecialMoveTimers();

        // Update animator parameters
        UpdateAnimations();

        // Handle sprite flipping
        HandleSpriteFlipping();

        // Handle jumping
        if (jumpPressed && isGrounded && !isDashing && !isSliding)
        {
            Jump();
            jumpPressed = false; // Reset jump input
        }
    }

    // Input System callback for movement (Send Messages)
    public void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    // Input System callback for jumping (Send Messages)
    public void OnJump(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            jumpPressed = true;
            // Trigger jump animation
            if (animator != null)
            {
                animator.SetTrigger("Jump");
            }
        }
    }

    // Alternative methods for Invoke Unity Events
    public void OnMoveInput(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    public void OnJumpInput(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            jumpPressed = true;
            // Trigger jump animation
            if (animator != null)
            {
                animator.SetTrigger("Jump");
            }
        }
    }

    // Input System callbacks for Slide and Dash (Send Messages)
    public void OnSlide(InputAction.CallbackContext context)
    {
        if (context.performed && isGrounded && !isDashing && !isSliding)
        {
            StartSlide();
        }
    }

    public void OnDash(InputAction.CallbackContext context)
    {
        if (context.performed && !isDashing && !isSliding)
        {
            StartDash();
        }
    }

    // Alternative methods for Invoke Unity Events
    public void OnSlideInput(InputAction.CallbackContext context)
    {
        if (context.performed && isGrounded && !isDashing && !isSliding)
        {
            StartSlide();
        }
    }

    public void OnDashInput(InputAction.CallbackContext context)
    {
        if (context.performed && !isDashing && !isSliding)
        {
            StartDash();
        }
    }

    void FixedUpdate()
    {
        // Apply horizontal movement
        MovePlayer();
    }

    void MovePlayer()
    {
        // Don't apply normal movement during special moves
        if (isDashing || isSliding)
            return;

        // Move the player horizontally using the new input system
        rb.linearVelocity = new Vector2(moveInput.x * moveSpeed, rb.linearVelocity.y);
    }

    void Jump()
    {
        // Apply jump force
        rb.linearVelocity = new Vector2(rb.linearVelocity.x, jumpForce);
    }

    void CheckGrounded()
    {
        // Check if the player is touching the ground
        isGrounded = Physics2D.OverlapCircle(groundCheck.position, groundCheckRadius, groundLayerMask);
    }

    void UpdateAnimations()
    {
        if (animator != null)
        {
            // Set speed parameter for idle/run transitions (only if not in special moves)
            float speed = (isDashing || isSliding) ? 0 : Mathf.Abs(moveInput.x);
            animator.SetFloat("Speed", speed);

            // Set grounded parameter
            animator.SetBool("IsGrounded", isGrounded);
        }
    }

    void HandleSpriteFlipping()
    {
        // Flip sprite based on movement direction (only if not in special moves)
        if (!isDashing && !isSliding && moveInput.x != 0)
        {
            if (moveInput.x > 0 && !facingRight)
            {
                Flip();
            }
            else if (moveInput.x < 0 && facingRight)
            {
                Flip();
            }
        }
    }

    void Flip()
    {
        facingRight = !facingRight;
        if (spriteRenderer != null)
        {
            spriteRenderer.flipX = !facingRight;
        }
    }

    void HandleSpecialMoveTimers()
    {
        // Handle dash timer
        if (isDashing)
        {
            dashTimer -= Time.deltaTime;
            if (dashTimer <= 0)
            {
                isDashing = false;
            }
        }

        // Handle slide timer
        if (isSliding)
        {
            slideTimer -= Time.deltaTime;
            if (slideTimer <= 0)
            {
                isSliding = false;
            }
        }
    }

    void StartDash()
    {
        isDashing = true;
        dashTimer = dashDuration;

        // Apply dash force in the direction the player is facing
        float dashDirection = facingRight ? 1f : -1f;
        rb.linearVelocity = new Vector2(dashDirection * dashForce, rb.linearVelocity.y);

        // Trigger dash animation
        if (animator != null)
        {
            animator.SetTrigger("Dash");
        }
    }

    void StartSlide()
    {
        isSliding = true;
        slideTimer = slideDuration;

        // Apply slide force in the direction the player is facing
        float slideDirection = facingRight ? 1f : -1f;
        rb.linearVelocity = new Vector2(slideDirection * slideForce, rb.linearVelocity.y);

        // Trigger slide animation
        if (animator != null)
        {
            animator.SetTrigger("Slide");
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw the ground check circle in the scene view
        if (groundCheck != null)
        {
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundCheckRadius);
        }
    }
}
