using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerMovement : MonoBehaviour
{
    [Header("Movement Settings")]
    public float moveSpeed = 5f;
    public float jumpForce = 10f;

    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundCheckRadius = 0.2f;
    public LayerMask groundLayerMask;

    private Rigidbody2D rb;
    private Animator animator;
    private bool isGrounded;
    private Vector2 moveInput;
    private bool jumpPressed;

    void Start()
    {
        // Get the Rigidbody2D component
        rb = GetComponent<Rigidbody2D>();

        // Get the Animator component
        animator = GetComponent<Animator>();

        // If no ground check transform is assigned, create one
        if (groundCheck == null)
        {
            GameObject groundCheckObj = new GameObject("GroundCheck");
            groundCheckObj.transform.SetParent(transform);
            groundCheckObj.transform.localPosition = new Vector3(0, -0.5f, 0);
            groundCheck = groundCheckObj.transform;
        }
    }

    void Update()
    {
        // Check if player is grounded
        CheckGrounded();

        // Update animator parameters
        UpdateAnimations();

        // Handle jumping
        if (jumpPressed && isGrounded)
        {
            Jump();
            jumpPressed = false; // Reset jump input
        }
    }

    // Input System callback for movement (Send Messages)
    public void OnMove(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    // Input System callback for jumping (Send Messages)
    public void OnJump(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            jumpPressed = true;
            // Trigger jump animation
            if (animator != null)
            {
                animator.SetTrigger("Jump");
            }
        }
    }

    // Alternative methods for Invoke Unity Events
    public void OnMoveInput(InputAction.CallbackContext context)
    {
        moveInput = context.ReadValue<Vector2>();
    }

    public void OnJumpInput(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            jumpPressed = true;
            // Trigger jump animation
            if (animator != null)
            {
                animator.SetTrigger("Jump");
            }
        }
    }

    void FixedUpdate()
    {
        // Apply horizontal movement
        MovePlayer();
    }

    void MovePlayer()
    {
        // Move the player horizontally using the new input system
        rb.linearVelocity = new Vector2(moveInput.x * moveSpeed, rb.linearVelocity.y);
    }

    void Jump()
    {
        // Apply jump force
        rb.linearVelocity = new Vector2(rb.linearVelocity.x, jumpForce);
    }

    void CheckGrounded()
    {
        // Check if the player is touching the ground
        isGrounded = Physics2D.OverlapCircle(groundCheck.position, groundCheckRadius, groundLayerMask);
    }

    void UpdateAnimations()
    {
        if (animator != null)
        {
            // Set speed parameter for idle/run transitions
            float speed = Mathf.Abs(moveInput.x);
            animator.SetFloat("Speed", speed);

            // Set grounded parameter
            animator.SetBool("IsGrounded", isGrounded);
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw the ground check circle in the scene view
        if (groundCheck != null)
        {
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundCheckRadius);
        }
    }
}
