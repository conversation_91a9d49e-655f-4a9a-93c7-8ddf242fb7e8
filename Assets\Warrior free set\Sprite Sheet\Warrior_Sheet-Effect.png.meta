fileFormatVersion: 2
guid: ae72133dacba6ec4eb33ecde48f50e56
AssetOrigin:
  serializedVersion: 1
  productId: 195707
  packageName: Warrior Free Asset
  packageVersion: 1.0
  assetPath: Assets/Warrior free set/Sprite Sheet/Warrior_Sheet-Effect.png
  uploadId: 434362
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -3318188226485093758
    second: Warrior_Sheet-Effect_0
  - first:
      213: -1938354968663525734
    second: Warrior_Sheet-Effect_1
  - first:
      213: -4486162353907066099
    second: Warrior_Sheet-Effect_2
  - first:
      213: -7105675749794256429
    second: Warrior_Sheet-Effect_3
  - first:
      213: 932576740597027765
    second: Warrior_Sheet-Effect_4
  - first:
      213: 8053934741035486310
    second: Warrior_Sheet-Effect_5
  - first:
      213: 4312510042007804440
    second: Warrior_Sheet-Effect_6
  - first:
      213: -3637831218265681369
    second: Warrior_Sheet-Effect_7
  - first:
      213: 7890055846987212836
    second: Warrior_Sheet-Effect_8
  - first:
      213: -1755831512471263806
    second: Warrior_Sheet-Effect_9
  - first:
      213: -7157502479376193569
    second: Warrior_Sheet-Effect_10
  - first:
      213: -6179113146597259875
    second: Warrior_Sheet-Effect_11
  - first:
      213: -7195227389596926799
    second: Warrior_Sheet-Effect_12
  - first:
      213: -7083850147136336687
    second: Warrior_Sheet-Effect_13
  - first:
      213: -7879541775824577086
    second: Warrior_Sheet-Effect_14
  - first:
      213: -6442761953431332421
    second: Warrior_Sheet-Effect_15
  - first:
      213: -7823500323640422531
    second: Warrior_Sheet-Effect_16
  - first:
      213: 3272844186751121127
    second: Warrior_Sheet-Effect_17
  - first:
      213: 8629003983396144442
    second: Warrior_Sheet-Effect_18
  - first:
      213: -6211648682532063752
    second: Warrior_Sheet-Effect_19
  - first:
      213: 4804923502247703499
    second: Warrior_Sheet-Effect_20
  - first:
      213: -6253208296904073923
    second: Warrior_Sheet-Effect_21
  - first:
      213: -381468241496348146
    second: Warrior_Sheet-Effect_22
  - first:
      213: -2164224423321271568
    second: Warrior_Sheet-Effect_23
  - first:
      213: 7378919880078719499
    second: Warrior_Sheet-Effect_24
  - first:
      213: -50406204238961237
    second: Warrior_Sheet-Effect_25
  - first:
      213: 2833012263988642564
    second: Warrior_Sheet-Effect_26
  - first:
      213: 1619122817114066791
    second: Warrior_Sheet-Effect_27
  - first:
      213: -3728085103066422436
    second: Warrior_Sheet-Effect_28
  - first:
      213: 5309330118571133426
    second: Warrior_Sheet-Effect_29
  - first:
      213: 2138419889203400846
    second: Warrior_Sheet-Effect_30
  - first:
      213: -3793288683446833867
    second: Warrior_Sheet-Effect_31
  - first:
      213: 7809884292531708377
    second: Warrior_Sheet-Effect_32
  - first:
      213: 8758733799639075308
    second: Warrior_Sheet-Effect_33
  - first:
      213: 4273360486593703493
    second: Warrior_Sheet-Effect_34
  - first:
      213: 8740081516789230483
    second: Warrior_Sheet-Effect_35
  - first:
      213: 4245071895355107723
    second: Warrior_Sheet-Effect_36
  - first:
      213: 8207571220096118977
    second: Warrior_Sheet-Effect_37
  - first:
      213: 7910466545735013580
    second: Warrior_Sheet-Effect_38
  - first:
      213: -4360246032673291897
    second: Warrior_Sheet-Effect_39
  - first:
      213: -6130774247032492815
    second: Warrior_Sheet-Effect_40
  - first:
      213: -7784280053648753441
    second: Warrior_Sheet-Effect_41
  - first:
      213: -8105224185696963899
    second: Warrior_Sheet-Effect_42
  - first:
      213: 565109345717033213
    second: Warrior_Sheet-Effect_43
  - first:
      213: 3578844115799939244
    second: Warrior_Sheet-Effect_44
  - first:
      213: -8346973252730123848
    second: Warrior_Sheet-Effect_45
  - first:
      213: 3675700599212663255
    second: Warrior_Sheet-Effect_46
  - first:
      213: -6199907869733816761
    second: Warrior_Sheet-Effect_47
  - first:
      213: -9068822172998705990
    second: Warrior_Sheet-Effect_48
  - first:
      213: 4778756267546337206
    second: Warrior_Sheet-Effect_49
  - first:
      213: -5417263232604538632
    second: Warrior_Sheet-Effect_50
  - first:
      213: -8179423753346160949
    second: Warrior_Sheet-Effect_51
  - first:
      213: -1605837713619785204
    second: Warrior_Sheet-Effect_52
  - first:
      213: 8858915403469927737
    second: Warrior_Sheet-Effect_53
  - first:
      213: 5335032761891035287
    second: Warrior_Sheet-Effect_54
  - first:
      213: 8562263981820643917
    second: Warrior_Sheet-Effect_55
  - first:
      213: 7739141928638578783
    second: Warrior_Sheet-Effect_56
  - first:
      213: -1765949524758486658
    second: Warrior_Sheet-Effect_57
  - first:
      213: -1406694122392401829
    second: Warrior_Sheet-Effect_58
  - first:
      213: 3914160388638673933
    second: Warrior_Sheet-Effect_59
  - first:
      213: 709901728315876072
    second: Warrior_Sheet-Effect_60
  - first:
      213: -5647377392427310024
    second: Warrior_Sheet-Effect_61
  - first:
      213: -5110843521143403644
    second: Warrior_Sheet-Effect_62
  - first:
      213: -8626763743991069669
    second: Warrior_Sheet-Effect_63
  - first:
      213: -8669300221853394086
    second: Warrior_Sheet-Effect_64
  - first:
      213: -8606690382453196554
    second: Warrior_Sheet-Effect_65
  - first:
      213: -1003838945947052602
    second: Warrior_Sheet-Effect_66
  - first:
      213: 1786230212584650817
    second: Warrior_Sheet-Effect_67
  - first:
      213: 2896272659956946786
    second: Warrior_Sheet-Effect_68
  - first:
      213: 2127461698910248042
    second: Warrior_Sheet-Effect_69
  - first:
      213: 8874842850510428824
    second: Warrior_Sheet-Effect_70
  - first:
      213: -1175057978942713942
    second: Warrior_Sheet-Effect_71
  - first:
      213: 8396084745013482390
    second: Warrior_Sheet-Effect_72
  - first:
      213: -5219189033981844993
    second: Warrior_Sheet-Effect_73
  - first:
      213: 8048201136183370311
    second: Warrior_Sheet-Effect_74
  - first:
      213: -5008098951225157325
    second: Warrior_Sheet-Effect_75
  - first:
      213: 3500951761227251082
    second: Warrior_Sheet-Effect_76
  - first:
      213: -848416997838638187
    second: Warrior_Sheet-Effect_77
  - first:
      213: -1594377132886445991
    second: Warrior_Sheet-Effect_78
  - first:
      213: -7939720171757009327
    second: Warrior_Sheet-Effect_79
  - first:
      213: 4278741268874708782
    second: Warrior_Sheet-Effect_80
  - first:
      213: -5551026827497223419
    second: Warrior_Sheet-Effect_81
  - first:
      213: -2437237654950474820
    second: Warrior_Sheet-Effect_82
  - first:
      213: -5853263684127566195
    second: Warrior_Sheet-Effect_83
  - first:
      213: 806391716420303315
    second: Warrior_Sheet-Effect_84
  - first:
      213: -7703501868747715773
    second: Warrior_Sheet-Effect_85
  - first:
      213: -5742998253431553049
    second: Warrior_Sheet-Effect_86
  - first:
      213: 1550331120443448429
    second: Warrior_Sheet-Effect_87
  - first:
      213: -3380132617870049673
    second: Warrior_Sheet-Effect_88
  - first:
      213: -6656010539272679822
    second: Warrior_Sheet-Effect_89
  - first:
      213: -6761883565659206456
    second: Warrior_Sheet-Effect_90
  - first:
      213: -2106872485169043283
    second: Warrior_Sheet-Effect_91
  - first:
      213: -5691269341329648135
    second: Warrior_Sheet-Effect_92
  - first:
      213: -8491356359149837760
    second: Warrior_Sheet-Effect_93
  - first:
      213: -4479905760731915366
    second: Warrior_Sheet-Effect_94
  - first:
      213: -7781806043594523482
    second: Warrior_Sheet-Effect_95
  - first:
      213: -2493374651010981141
    second: Warrior_Sheet-Effect_96
  - first:
      213: -7685447711378399537
    second: Warrior_Sheet-Effect_97
  - first:
      213: 787512443710130017
    second: Warrior_Sheet-Effect_98
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WindowsStoreApps
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_0
      rect:
        serializedVersion: 2
        x: 0
        y: 704
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 28af876736d63f1d0800000000000000
      internalID: -3318188226485093758
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_1
      rect:
        serializedVersion: 2
        x: 69
        y: 704
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a9672340d649915e0800000000000000
      internalID: -1938354968663525734
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_2
      rect:
        serializedVersion: 2
        x: 138
        y: 704
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d0b93aa9b13fdb1c0800000000000000
      internalID: -4486162353907066099
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_3
      rect:
        serializedVersion: 2
        x: 207
        y: 704
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d9e7649d81936d90800000000000000
      internalID: -7105675749794256429
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_4
      rect:
        serializedVersion: 2
        x: 276
        y: 704
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b3d315ca9d21fc00800000000000000
      internalID: 932576740597027765
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_5
      rect:
        serializedVersion: 2
        x: 345
        y: 704
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 66c0993b9f255cf60800000000000000
      internalID: 8053934741035486310
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_6
      rect:
        serializedVersion: 2
        x: 0
        y: 660
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 81a1e28d80d19db30800000000000000
      internalID: 4312510042007804440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_7
      rect:
        serializedVersion: 2
        x: 69
        y: 660
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7229272f7c3d38dc0800000000000000
      internalID: -3637831218265681369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_8
      rect:
        serializedVersion: 2
        x: 138
        y: 660
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 428aaa15dfb1f7d60800000000000000
      internalID: 7890055846987212836
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_9
      rect:
        serializedVersion: 2
        x: 207
        y: 660
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2c5a6bdca8802a7e0800000000000000
      internalID: -1755831512471263806
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_10
      rect:
        serializedVersion: 2
        x: 276
        y: 660
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd7edc5ca617bac90800000000000000
      internalID: -7157502479376193569
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_11
      rect:
        serializedVersion: 2
        x: 345
        y: 660
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d95cf1ed4516f3aa0800000000000000
      internalID: -6179113146597259875
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_12
      rect:
        serializedVersion: 2
        x: 0
        y: 616
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1bcf9e4afca652c90800000000000000
      internalID: -7195227389596926799
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_13
      rect:
        serializedVersion: 2
        x: 69
        y: 616
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1dc07d1b2db11bd90800000000000000
      internalID: -7083850147136336687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_14
      rect:
        serializedVersion: 2
        x: 138
        y: 616
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2cd1023a08e36a290800000000000000
      internalID: -7879541775824577086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_15
      rect:
        serializedVersion: 2
        x: 207
        y: 616
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb9d2597326b696a0800000000000000
      internalID: -6442761953431332421
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_16
      rect:
        serializedVersion: 2
        x: 276
        y: 616
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d7f424508e75d6390800000000000000
      internalID: -7823500323640422531
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_17
      rect:
        serializedVersion: 2
        x: 345
        y: 616
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7e6b084927a7b6d20800000000000000
      internalID: 3272844186751121127
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_18
      rect:
        serializedVersion: 2
        x: 0
        y: 572
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a3502cb876160c770800000000000000
      internalID: 8629003983396144442
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_19
      rect:
        serializedVersion: 2
        x: 69
        y: 572
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8f537281f6acbc9a0800000000000000
      internalID: -6211648682532063752
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_20
      rect:
        serializedVersion: 2
        x: 138
        y: 572
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bc3766cb7748ea240800000000000000
      internalID: 4804923502247703499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_21
      rect:
        serializedVersion: 2
        x: 207
        y: 572
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d3dd1845f242839a0800000000000000
      internalID: -6253208296904073923
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_22
      rect:
        serializedVersion: 2
        x: 276
        y: 572
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e0a648cc7a0c4baf0800000000000000
      internalID: -381468241496348146
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_23
      rect:
        serializedVersion: 2
        x: 345
        y: 572
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0f206b18a5127f1e0800000000000000
      internalID: -2164224423321271568
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_24
      rect:
        serializedVersion: 2
        x: 0
        y: 528
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b0af8262980376660800000000000000
      internalID: 7378919880078719499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_25
      rect:
        serializedVersion: 2
        x: 69
        y: 528
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ba56cd503dbec4ff0800000000000000
      internalID: -50406204238961237
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_26
      rect:
        serializedVersion: 2
        x: 138
        y: 528
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4034e57e4a1e05720800000000000000
      internalID: 2833012263988642564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_27
      rect:
        serializedVersion: 2
        x: 207
        y: 528
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 76f40d9af97487610800000000000000
      internalID: 1619122817114066791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_28
      rect:
        serializedVersion: 2
        x: 276
        y: 528
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c53ec4bd95e234cc0800000000000000
      internalID: -3728085103066422436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_29
      rect:
        serializedVersion: 2
        x: 345
        y: 528
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2f907b0ec978ea940800000000000000
      internalID: 5309330118571133426
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_30
      rect:
        serializedVersion: 2
        x: 0
        y: 484
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e8059943f813dad10800000000000000
      internalID: 2138419889203400846
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_31
      rect:
        serializedVersion: 2
        x: 69
        y: 484
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 53d64a44a088b5bc0800000000000000
      internalID: -3793288683446833867
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_32
      rect:
        serializedVersion: 2
        x: 138
        y: 484
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9d16141b268426c60800000000000000
      internalID: 7809884292531708377
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_33
      rect:
        serializedVersion: 2
        x: 207
        y: 484
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ce9c1b92cf54d8970800000000000000
      internalID: 8758733799639075308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_34
      rect:
        serializedVersion: 2
        x: 276
        y: 484
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 54eab98a8b60e4b30800000000000000
      internalID: 4273360486593703493
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_35
      rect:
        serializedVersion: 2
        x: 345
        y: 484
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 39bed2425d10b4970800000000000000
      internalID: 8740081516789230483
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_36
      rect:
        serializedVersion: 2
        x: 0
        y: 440
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b89a2a0c56689ea30800000000000000
      internalID: 4245071895355107723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_37
      rect:
        serializedVersion: 2
        x: 69
        y: 440
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1c07c820a8627e170800000000000000
      internalID: 8207571220096118977
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_38
      rect:
        serializedVersion: 2
        x: 138
        y: 440
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cc49522796f97cd60800000000000000
      internalID: 7910466545735013580
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_39
      rect:
        serializedVersion: 2
        x: 207
        y: 440
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 781c339b55b4d73c0800000000000000
      internalID: -4360246032673291897
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_40
      rect:
        serializedVersion: 2
        x: 276
        y: 440
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1fce387fd4d1beaa0800000000000000
      internalID: -6130774247032492815
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_41
      rect:
        serializedVersion: 2
        x: 345
        y: 440
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fdc7b1ab88ea8f390800000000000000
      internalID: -7784280053648753441
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_42
      rect:
        serializedVersion: 2
        x: 0
        y: 396
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5c2b0a2eb85748f80800000000000000
      internalID: -8105224185696963899
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_43
      rect:
        serializedVersion: 2
        x: 69
        y: 396
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: df897b2b4fba7d700800000000000000
      internalID: 565109345717033213
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_44
      rect:
        serializedVersion: 2
        x: 138
        y: 396
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ca890c403cb9aa130800000000000000
      internalID: 3578844115799939244
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_45
      rect:
        serializedVersion: 2
        x: 207
        y: 396
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8b985510d08992c80800000000000000
      internalID: -8346973252730123848
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_46
      rect:
        serializedVersion: 2
        x: 276
        y: 396
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7d1e2c46836b20330800000000000000
      internalID: 3675700599212663255
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_47
      rect:
        serializedVersion: 2
        x: 345
        y: 396
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 746fd5814a085f9a0800000000000000
      internalID: -6199907869733816761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_48
      rect:
        serializedVersion: 2
        x: 0
        y: 352
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: abcd9374842152280800000000000000
      internalID: -9068822172998705990
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_49
      rect:
        serializedVersion: 2
        x: 69
        y: 352
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6bf5543a18d815240800000000000000
      internalID: 4778756267546337206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_50
      rect:
        serializedVersion: 2
        x: 138
        y: 352
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8f872d5bac302d4b0800000000000000
      internalID: -5417263232604538632
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_51
      rect:
        serializedVersion: 2
        x: 207
        y: 352
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bc6e86ace69dc7e80800000000000000
      internalID: -8179423753346160949
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_52
      rect:
        serializedVersion: 2
        x: 276
        y: 352
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c0a64c88b1be6b9e0800000000000000
      internalID: -1605837713619785204
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_53
      rect:
        serializedVersion: 2
        x: 345
        y: 352
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 935bf077f9031fa70800000000000000
      internalID: 8858915403469927737
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_54
      rect:
        serializedVersion: 2
        x: 0
        y: 308
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 798169dd708d90a40800000000000000
      internalID: 5335032761891035287
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_55
      rect:
        serializedVersion: 2
        x: 69
        y: 308
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d4a8a850ab543d670800000000000000
      internalID: 8562263981820643917
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_56
      rect:
        serializedVersion: 2
        x: 138
        y: 308
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f5085d29494f66b60800000000000000
      internalID: 7739141928638578783
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_57
      rect:
        serializedVersion: 2
        x: 207
        y: 308
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e71208d73461e77e0800000000000000
      internalID: -1765949524758486658
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_58
      rect:
        serializedVersion: 2
        x: 276
        y: 308
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b58cb2f062b6a7ce0800000000000000
      internalID: -1406694122392401829
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_59
      rect:
        serializedVersion: 2
        x: 345
        y: 308
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d0c86b6a224e15630800000000000000
      internalID: 3914160388638673933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_60
      rect:
        serializedVersion: 2
        x: 0
        y: 264
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8e224b55cd31ad900800000000000000
      internalID: 709901728315876072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_61
      rect:
        serializedVersion: 2
        x: 69
        y: 264
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8346cc3ce2c70a1b0800000000000000
      internalID: -5647377392427310024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_62
      rect:
        serializedVersion: 2
        x: 138
        y: 264
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 48b04ab55e2a219b0800000000000000
      internalID: -5110843521143403644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_63
      rect:
        serializedVersion: 2
        x: 207
        y: 264
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b148136d414974880800000000000000
      internalID: -8626763743991069669
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_64
      rect:
        serializedVersion: 2
        x: 276
        y: 264
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a5b9631516570b780800000000000000
      internalID: -8669300221853394086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_65
      rect:
        serializedVersion: 2
        x: 345
        y: 264
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6f83b1682b4ee8880800000000000000
      internalID: -8606690382453196554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_66
      rect:
        serializedVersion: 2
        x: 0
        y: 220
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6c59f171cc5a112f0800000000000000
      internalID: -1003838945947052602
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_67
      rect:
        serializedVersion: 2
        x: 69
        y: 220
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 148bd2c1ae6f9c810800000000000000
      internalID: 1786230212584650817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_68
      rect:
        serializedVersion: 2
        x: 138
        y: 220
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 267d86d93a0a13820800000000000000
      internalID: 2896272659956946786
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_69
      rect:
        serializedVersion: 2
        x: 207
        y: 220
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a6c5bc4b423468d10800000000000000
      internalID: 2127461698910248042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_70
      rect:
        serializedVersion: 2
        x: 276
        y: 220
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 89ad6163d86c92b70800000000000000
      internalID: 8874842850510428824
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_71
      rect:
        serializedVersion: 2
        x: 345
        y: 220
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aaf81ae6efa51bfe0800000000000000
      internalID: -1175057978942713942
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_72
      rect:
        serializedVersion: 2
        x: 0
        y: 176
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 693da9a5792e48470800000000000000
      internalID: 8396084745013482390
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_73
      rect:
        serializedVersion: 2
        x: 69
        y: 176
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ff1e5edd937b197b0800000000000000
      internalID: -5219189033981844993
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_74
      rect:
        serializedVersion: 2
        x: 138
        y: 176
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 742ea1bca44f0bf60800000000000000
      internalID: 8048201136183370311
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_75
      rect:
        serializedVersion: 2
        x: 207
        y: 176
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 339d734b988af7ab0800000000000000
      internalID: -5008098951225157325
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_76
      rect:
        serializedVersion: 2
        x: 276
        y: 176
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a8d7ab7f311e59030800000000000000
      internalID: 3500951761227251082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_77
      rect:
        serializedVersion: 2
        x: 345
        y: 176
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 593bc6e4c31d934f0800000000000000
      internalID: -848416997838638187
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_78
      rect:
        serializedVersion: 2
        x: 0
        y: 132
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9503c4ce172afd9e0800000000000000
      internalID: -1594377132886445991
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_79
      rect:
        serializedVersion: 2
        x: 69
        y: 132
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1568b6af19270d190800000000000000
      internalID: -7939720171757009327
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_80
      rect:
        serializedVersion: 2
        x: 138
        y: 132
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e27c419a384216b30800000000000000
      internalID: 4278741268874708782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_81
      rect:
        serializedVersion: 2
        x: 207
        y: 132
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 50b4d50d28ac6f2b0800000000000000
      internalID: -5551026827497223419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_82
      rect:
        serializedVersion: 2
        x: 276
        y: 132
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cbb887f04413d2ed0800000000000000
      internalID: -2437237654950474820
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_83
      rect:
        serializedVersion: 2
        x: 345
        y: 132
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d86d1e2f0b705cea0800000000000000
      internalID: -5853263684127566195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_84
      rect:
        serializedVersion: 2
        x: 0
        y: 88
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d9bc0f5ef0e03b00800000000000000
      internalID: 806391716420303315
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_85
      rect:
        serializedVersion: 2
        x: 69
        y: 88
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 34b0cb28dd9a71590800000000000000
      internalID: -7703501868747715773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_86
      rect:
        serializedVersion: 2
        x: 138
        y: 88
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7e306862485cc40b0800000000000000
      internalID: -5742998253431553049
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_87
      rect:
        serializedVersion: 2
        x: 207
        y: 88
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d6cd346a0f1e38510800000000000000
      internalID: 1550331120443448429
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_88
      rect:
        serializedVersion: 2
        x: 276
        y: 88
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 77ef422fa4b5711d0800000000000000
      internalID: -3380132617870049673
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_89
      rect:
        serializedVersion: 2
        x: 345
        y: 88
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 27e4569fda911a3a0800000000000000
      internalID: -6656010539272679822
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_90
      rect:
        serializedVersion: 2
        x: 0
        y: 44
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8ccc45819b6f822a0800000000000000
      internalID: -6761883565659206456
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_91
      rect:
        serializedVersion: 2
        x: 69
        y: 44
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da8647e23a2e2c2e0800000000000000
      internalID: -2106872485169043283
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_92
      rect:
        serializedVersion: 2
        x: 138
        y: 44
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9f599fc00bc8401b0800000000000000
      internalID: -5691269341329648135
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_93
      rect:
        serializedVersion: 2
        x: 207
        y: 44
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 042ae976164a82a80800000000000000
      internalID: -8491356359149837760
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_94
      rect:
        serializedVersion: 2
        x: 276
        y: 44
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a9b1958327d24d1c0800000000000000
      internalID: -4479905760731915366
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_95
      rect:
        serializedVersion: 2
        x: 345
        y: 44
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ac7f8412a8710490800000000000000
      internalID: -7781806043594523482
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_96
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: be2e68227f0c56dd0800000000000000
      internalID: -2493374651010981141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_97
      rect:
        serializedVersion: 2
        x: 69
        y: 0
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fce9377960ec75590800000000000000
      internalID: -7685447711378399537
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Warrior_Sheet-Effect_98
      rect:
        serializedVersion: 2
        x: 138
        y: 0
        width: 69
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 167ca87256ecdea00800000000000000
      internalID: 787512443710130017
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Warrior_Sheet-Effect_0: -3318188226485093758
      Warrior_Sheet-Effect_1: -1938354968663525734
      Warrior_Sheet-Effect_10: -7157502479376193569
      Warrior_Sheet-Effect_11: -6179113146597259875
      Warrior_Sheet-Effect_12: -7195227389596926799
      Warrior_Sheet-Effect_13: -7083850147136336687
      Warrior_Sheet-Effect_14: -7879541775824577086
      Warrior_Sheet-Effect_15: -6442761953431332421
      Warrior_Sheet-Effect_16: -7823500323640422531
      Warrior_Sheet-Effect_17: 3272844186751121127
      Warrior_Sheet-Effect_18: 8629003983396144442
      Warrior_Sheet-Effect_19: -6211648682532063752
      Warrior_Sheet-Effect_2: -4486162353907066099
      Warrior_Sheet-Effect_20: 4804923502247703499
      Warrior_Sheet-Effect_21: -6253208296904073923
      Warrior_Sheet-Effect_22: -381468241496348146
      Warrior_Sheet-Effect_23: -2164224423321271568
      Warrior_Sheet-Effect_24: 7378919880078719499
      Warrior_Sheet-Effect_25: -50406204238961237
      Warrior_Sheet-Effect_26: 2833012263988642564
      Warrior_Sheet-Effect_27: 1619122817114066791
      Warrior_Sheet-Effect_28: -3728085103066422436
      Warrior_Sheet-Effect_29: 5309330118571133426
      Warrior_Sheet-Effect_3: -7105675749794256429
      Warrior_Sheet-Effect_30: 2138419889203400846
      Warrior_Sheet-Effect_31: -3793288683446833867
      Warrior_Sheet-Effect_32: 7809884292531708377
      Warrior_Sheet-Effect_33: 8758733799639075308
      Warrior_Sheet-Effect_34: 4273360486593703493
      Warrior_Sheet-Effect_35: 8740081516789230483
      Warrior_Sheet-Effect_36: 4245071895355107723
      Warrior_Sheet-Effect_37: 8207571220096118977
      Warrior_Sheet-Effect_38: 7910466545735013580
      Warrior_Sheet-Effect_39: -4360246032673291897
      Warrior_Sheet-Effect_4: 932576740597027765
      Warrior_Sheet-Effect_40: -6130774247032492815
      Warrior_Sheet-Effect_41: -7784280053648753441
      Warrior_Sheet-Effect_42: -8105224185696963899
      Warrior_Sheet-Effect_43: 565109345717033213
      Warrior_Sheet-Effect_44: 3578844115799939244
      Warrior_Sheet-Effect_45: -8346973252730123848
      Warrior_Sheet-Effect_46: 3675700599212663255
      Warrior_Sheet-Effect_47: -6199907869733816761
      Warrior_Sheet-Effect_48: -9068822172998705990
      Warrior_Sheet-Effect_49: 4778756267546337206
      Warrior_Sheet-Effect_5: 8053934741035486310
      Warrior_Sheet-Effect_50: -5417263232604538632
      Warrior_Sheet-Effect_51: -8179423753346160949
      Warrior_Sheet-Effect_52: -1605837713619785204
      Warrior_Sheet-Effect_53: 8858915403469927737
      Warrior_Sheet-Effect_54: 5335032761891035287
      Warrior_Sheet-Effect_55: 8562263981820643917
      Warrior_Sheet-Effect_56: 7739141928638578783
      Warrior_Sheet-Effect_57: -1765949524758486658
      Warrior_Sheet-Effect_58: -1406694122392401829
      Warrior_Sheet-Effect_59: 3914160388638673933
      Warrior_Sheet-Effect_6: 4312510042007804440
      Warrior_Sheet-Effect_60: 709901728315876072
      Warrior_Sheet-Effect_61: -5647377392427310024
      Warrior_Sheet-Effect_62: -5110843521143403644
      Warrior_Sheet-Effect_63: -8626763743991069669
      Warrior_Sheet-Effect_64: -8669300221853394086
      Warrior_Sheet-Effect_65: -8606690382453196554
      Warrior_Sheet-Effect_66: -1003838945947052602
      Warrior_Sheet-Effect_67: 1786230212584650817
      Warrior_Sheet-Effect_68: 2896272659956946786
      Warrior_Sheet-Effect_69: 2127461698910248042
      Warrior_Sheet-Effect_7: -3637831218265681369
      Warrior_Sheet-Effect_70: 8874842850510428824
      Warrior_Sheet-Effect_71: -1175057978942713942
      Warrior_Sheet-Effect_72: 8396084745013482390
      Warrior_Sheet-Effect_73: -5219189033981844993
      Warrior_Sheet-Effect_74: 8048201136183370311
      Warrior_Sheet-Effect_75: -5008098951225157325
      Warrior_Sheet-Effect_76: 3500951761227251082
      Warrior_Sheet-Effect_77: -848416997838638187
      Warrior_Sheet-Effect_78: -1594377132886445991
      Warrior_Sheet-Effect_79: -7939720171757009327
      Warrior_Sheet-Effect_8: 7890055846987212836
      Warrior_Sheet-Effect_80: 4278741268874708782
      Warrior_Sheet-Effect_81: -5551026827497223419
      Warrior_Sheet-Effect_82: -2437237654950474820
      Warrior_Sheet-Effect_83: -5853263684127566195
      Warrior_Sheet-Effect_84: 806391716420303315
      Warrior_Sheet-Effect_85: -7703501868747715773
      Warrior_Sheet-Effect_86: -5742998253431553049
      Warrior_Sheet-Effect_87: 1550331120443448429
      Warrior_Sheet-Effect_88: -3380132617870049673
      Warrior_Sheet-Effect_89: -6656010539272679822
      Warrior_Sheet-Effect_9: -1755831512471263806
      Warrior_Sheet-Effect_90: -6761883565659206456
      Warrior_Sheet-Effect_91: -2106872485169043283
      Warrior_Sheet-Effect_92: -5691269341329648135
      Warrior_Sheet-Effect_93: -8491356359149837760
      Warrior_Sheet-Effect_94: -4479905760731915366
      Warrior_Sheet-Effect_95: -7781806043594523482
      Warrior_Sheet-Effect_96: -2493374651010981141
      Warrior_Sheet-Effect_97: -7685447711378399537
      Warrior_Sheet-Effect_98: 787512443710130017
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
